#!/usr/bin/env python3

"""
Test script to verify memory optimizations for multi-GPU training.
This script tests the soil model compilation with different memory settings.
"""

import os
import torch
import argparse
from isaaclab.app import AppLauncher

# Add argparse arguments
parser = argparse.ArgumentParser(description="Test memory optimizations for soil model.")
parser.add_argument("--num_envs", type=int, default=16000, help="Number of environments to test.")
parser.add_argument("--disable_cudagraphs", action="store_true", help="Disable CUDA graphs.")
parser.add_argument("--compilation_mode", type=str, default="default", 
                   choices=["default", "reduce-overhead", "max-autotune"],
                   help="Torch compilation mode.")
AppLauncher.add_app_launcher_args(parser)
args_cli = parser.parse_args()
args_cli.headless = True

# Launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import gymnasium as gym
from isaaclab_tasks.utils import parse_env_cfg
import moleworks_ext.tasks  # noqa: F401

def test_memory_usage():
    """Test memory usage with different configurations."""
    
    print(f"Testing with {args_cli.num_envs} environments")
    print(f"CUDA graphs disabled: {args_cli.disable_cudagraphs}")
    print(f"Compilation mode: {args_cli.compilation_mode}")
    
    # Parse environment configuration
    env_cfg = parse_env_cfg("Isaac-m545-digging", num_envs=args_cli.num_envs)
    
    # Apply memory optimizations
    env_cfg.soil_model_cfg.disable_cudagraphs = args_cli.disable_cudagraphs
    env_cfg.soil_model_cfg.compilation_mode = args_cli.compilation_mode
    
    # Set PyTorch memory allocation for better memory management
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"
    
    print("\nGPU Memory before environment creation:")
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / 1024**3
            reserved = torch.cuda.memory_reserved(i) / 1024**3
            total = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"  GPU {i}: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved, {total:.2f}GB total")
    
    try:
        # Create environment
        print("\nCreating environment...")
        env = gym.make("Isaac-m545-digging", cfg=env_cfg)
        
        print("\nGPU Memory after environment creation:")
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                allocated = torch.cuda.memory_allocated(i) / 1024**3
                reserved = torch.cuda.memory_reserved(i) / 1024**3
                total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                free = total - reserved
                print(f"  GPU {i}: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved, {free:.2f}GB free")
        
        # Reset environment to trigger soil model compilation
        print("\nResetting environment (triggers soil model compilation)...")
        env.reset()
        
        print("\nGPU Memory after reset (compilation):")
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                allocated = torch.cuda.memory_allocated(i) / 1024**3
                reserved = torch.cuda.memory_reserved(i) / 1024**3
                total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                free = total - reserved
                print(f"  GPU {i}: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved, {free:.2f}GB free")
        
        # Run a few steps
        print("\nRunning simulation steps...")
        for i in range(10):
            actions = torch.zeros(env.action_space.shape, device=env.unwrapped.device)
            env.step(actions)
            if i == 0:
                print("First step completed successfully")
        
        print("\nGPU Memory after simulation steps:")
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                allocated = torch.cuda.memory_allocated(i) / 1024**3
                reserved = torch.cuda.memory_reserved(i) / 1024**3
                total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                free = total - reserved
                print(f"  GPU {i}: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved, {free:.2f}GB free")
        
        print("\n✅ Memory optimization test completed successfully!")
        
        # Close environment
        env.close()
        
    except torch.OutOfMemoryError as e:
        print(f"\n❌ Out of memory error: {e}")
        print("\nSuggestions:")
        print("- Reduce --num_envs")
        print("- Use --disable_cudagraphs")
        print("- Set compilation_mode to 'default'")
        return False
    except Exception as e:
        print(f"\n❌ Error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_memory_usage()
    simulation_app.close()
    exit(0 if success else 1)
