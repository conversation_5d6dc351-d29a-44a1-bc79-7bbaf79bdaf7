# Isaac Lab Docker Setup

## Overview
This guide details how to build, run, and use the Isaac Lab Docker containers for `<extension_name>`. For cluster operations (job submission and log sync), see the [Cluster Usage Guide](cluster_usage.md).

## Table of Contents

- [Prerequisites](#prerequisites)
- [Getting Started](#getting-started)
  - [Option 1: Pull Base Images from DockerHub](#option-1-pull-base-images-from-dockerhub)
  - [Option 2: Build Base Containers Locally](#option-2-build-base-containers-locally)
  - [Building Extension Containers](#building-extension-containers)
- [Running the Containers](#running-the-containers)
- [Development Container Setup](#development-container-setup)
- [ROS2 Container Setup](#ros2-container-setup)
  - [Building ROS2 Containers](#building-ros2-containers)
  - [Running ROS2 Containers](#running-ros2-containers)
  - [ROS2 Debugging](#ros2-debugging)
- [Additional Information](#additional-information)
- [Troubleshooting](#troubleshooting)

## Prerequisites
- **Docker**: Install Docker and Docker Compose.
- **Environment Files**: Create your environment files by copying the provided templates:
  ```bash
  # Copy template files and edit them with your details
  cp docker/.env.moleworks_ext.template docker/.env.moleworks_ext
  cp docker/.env.moleworks_ext-dev.template docker/.env.moleworks_ext-dev
  cp docker/.env.moleworks_ext-ros2.template docker/.env.moleworks_ext-ros2
  ```
  Then edit these files with your personal settings. These files are git-ignored to prevent committing credentials.
- **IsaacLab**: Symlink `isaaclab` in the repository root as `_isaaclab`. [IsaacLab-Internal Repository](https://github.com/leggedrobotics/IsaacLab-Internal/tree/main-rsl)


## Getting Started
There are several containers available:
- **isaac-lab-moleworks_ext-ros2**: For Isaac and ROS2 development.
- **isaac-lab-moleworks_ext**: For cluster deployment.
- **isaac-lab-moleworks_ext-dev**: For local Isaac development.
- **isaac-lab-moleworks_ext-dev-rootless**: For rootless Docker environments (e.g., student PCs).

Code must run inside Docker containers. First, you need to obtain or build the base images, then build your extension containers.

### Base Images

You have two options to get the required base images (isaac-lab-base and isaac-lab-ros2):

### Option 1: Pull Base Images from DockerHub
1. Request an access token from an admin.
2. Pull the base images:
   ```bash
   docker pull rslheap/isaac-lab-base:latest
   docker pull rslheap/isaac-lab-ros2:latest
   ```
3. Retag them for local use:
   ```bash
   docker tag rslheap/isaac-lab-base:latest isaac-lab-base:latest
   docker tag rslheap/isaac-lab-ros2:latest isaac-lab-ros2:latest
   ```

### Option 2: Build Base Containers Locally
1. **Checkout the Branch**  
   Clone the repository and switch to `moleworks`:  
   [IsaacLab-Internal Repository](https://github.com/leggedrobotics/IsaacLab-Internal/tree/main-rsl/docker)
2. **Build the Containers**  
   In the `docker` directory, run:
   ```bash
   cd docker
   docker compose --env-file .env.base --file docker-compose.yaml build isaac-lab-base
   docker compose --env-file .env.ros2 --file docker-compose.yaml build isaac-lab-ros2
   ```

### Building Extension Containers

**Important Notes:**
- The **dev** and **ros2** containers **MUST** be built locally because they incorporate user-specific variables (username and home directory) from the `.env.moleworks_ext-dev` and `.env.moleworks_ext-ros2` files.
- You can pull the **rootless** container from DockerHub (if available) since it runs as root and doesn't depend on user-specific variables.
- The standard container can be pulled from DockerHub or built locally.
- Always use the provided template files (`.env.moleworks_ext.template`, `.env.moleworks_ext-dev.template`, `.env.moleworks_ext-ros2.template`) to create your local configuration files, and avoid committing your personal environment files.

#### Build Standard and Development Containers
1. First, ensure your `.env.moleworks_ext` and `.env.moleworks_ext-dev` files have correct values for `HOST_HOME`, `DOCKER_USER_NAME`, and `DOCKER_USER_HOME`.
2. Build the containers:
   ```bash
   # Standard container for deployment
   docker compose --env-file .env.moleworks_ext --file docker-compose.yaml build isaac-lab-ext
   
   # Development container - MUST be built locally for user variables
   docker compose --env-file .env.moleworks_ext-dev --file docker-compose.yaml build isaac-lab-ext-dev
   ```

#### Build Rootless Container
```bash
docker compose --env-file .env.moleworks_ext-dev --file docker-compose.yaml build isaac-lab-ext-dev-rootless
```

#### Alternatively, Pull Pre-built Images from DockerHub
For the standard and rootless containers only, you can use pre-built images:
```bash
# Pull images
docker pull rslheap/isaac-lab-moleworks_ext:latest
docker pull rslheap/isaac-lab-moleworks_ext-dev-rootless:latest

# Retag them
docker tag rslheap/isaac-lab-moleworks_ext:latest isaac-lab-moleworks_ext:latest
docker tag rslheap/isaac-lab-moleworks_ext-dev-rootless:latest isaac-lab-moleworks_ext-dev-rootless:latest
```

For ROS2 container build instructions, see [ROS2 Container Setup](#ros2-container-setup).

## Running the Containers

### Standard Container
- **Run interactively:**
  ```bash
  docker compose --env-file .env.moleworks_ext --file docker-compose.yaml run --rm isaac-lab-ext
  docker compose --env-file .env.moleworks_ext-dev --file docker-compose.yaml run --rm isaac-lab-ext-dev-rootless

  ```
- **Attach to a running container:**
  ```bash
  docker compose --env-file .env.moleworks_ext --file docker-compose.yaml attach isaac-lab-ext
  docker compose --env-file .env.moleworks_ext-dev --file docker-compose.yaml attach --rm isaac-lab-ext-dev-rootless

  ```

## ROS2 Container Setup

The ROS2 container offers an integrated environment for ROS2 development with IsaacLab.

### Building ROS2 Containers
1. **Get the Base ROS2 Image**  
   You have two options:
   - **Option 1**: Follow the [instructions](https://isaac-sim.github.io/IsaacLab/main/source/deployment/docker.html#deployment-docker) to create the `isaac-lab-ros2` image locally.
   - **Option 2**: Pull from DockerHub and retag (as described in [Option 1: Pull Base Images from DockerHub](#option-1-pull-base-images-from-dockerhub)).

2. **Build the Extension ROS2 Container**  
   **Important**: The ROS2 container **MUST** be built locally as it incorporates user-specific variables from `.env.moleworks_ext-ros2`.
   
   First, ensure your `.env.moleworks_ext-ros2` file has correct values for `HOST_HOME`, `DOCKER_USER_NAME`, and `DOCKER_USER_HOME`.
   
   Then run:
   ```bash
   docker compose --env-file .env.moleworks_ext-ros2 --file docker-compose.yaml build isaac-lab-ext-ros2
   ```
   Make sure you pull from git-lfs and the submodlues

### Running ROS2 Containers
1. **Run the Container Interactively:**
   ```bash
   docker compose --env-file .env.moleworks_ext-ros2 --file docker-compose.yaml run --rm isaac-lab-ext-ros2
   ```
2. **Attach to a Running Container:**
   ```bash
   docker compose --env-file .env.moleworks_ext-ros2 --file docker-compose.yaml attach isaac-lab-ext-ros2
   ```

### Test ROS2 Containers
1. **Run the Container Interactively:**
   ```bash
   docker compose --env-file .env.moleworks_ext-ros2 --file docker-compose.yaml run --rm isaac-lab-ext-ros2
   ```
2. **Build and source the ros packages:**
   ```bash
   source /opt/ros/humble/setup.bash
   cd $EXT_PATH/source/moleworks_ext/moleworks_ext/ros/ros2_ws/
   colcon build
   source install/setup.bash
   ```
3. **Run some enviroment:**
   ```bash
   cd $EXT_PATH
   python source/moleworks_ext/moleworks_ext/ros/scripts/standalone_dig_env.py
   ```

## Additional Information
- **NVIDIA Drivers**: Ensure NVIDIA drivers are installed if using GPU resources.
- **Network Mode**: The container uses `host` network mode to share the host's network.

## Troubleshooting

### SSH Error
If you see:
```
docker compose --env-file .env.moleworks_ext-dev --file docker-compose.yaml run isaac-lab-ext-dev-root
WARN[0000] The "SSH_AUTH_SOCK" variable is not set. Defaulting to a blank string.
invalid spec: :/ssh-agent: empty section between colons
```
Run:
```bash
eval "$(ssh-agent -s)"
export SSH_AUTH_SOCK
```

### X11 Error When Starting IsaacSim
If you encounter an X11 error, run:
```bash
xauth list $DISPLAY > /tmp/.docker.xauth
chmod 666 /tmp/.docker.xauth
xhost +local:docker
```

### Other Issues
- **Docker Installation**: Confirm Docker is installed and running. Refer to the [official guide](https://docs.omniverse.nvidia.com/isaacsim/latest/installation/install_container.html).
- **Environment Variables**: Verify that `DISPLAY` is set correctly.

For cluster setup, see the [Cluster Usage Guide](cluster_usage.md).

### Running the Dev-Rootless Container
To run the dev-rootless container with the `.env.moleworks_ext-dev` file, use:
```bash
docker compose --env-file .env.moleworks_ext-dev --file docker-compose.yaml run --rm isaac-lab-ext-dev-rootless
```

