# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .soil_3d import Soil3D

import numpy as np
import torch
import torch.profiler


class SoilForces3D:
    def __init__(self, soil_model: "Soil3D", cfg):
        self.SM = soil_model
        self.SP = soil_model.soil_parameters
        self.SSP = soil_model.ssp
        self.bucket = soil_model.bucket_state
        self.n_envs = self.SM.n_envs
        self.device = self.SM.device
        self.cfg = cfg.soil_forces

        """
        INFO
        - fee only valid if SSP.beta in [0,pi]
            - SSP needs to exit soil!! otherwise fee forces are wrong/nonsense
        """

        # --- Basic Tensors ---
        self.zero_tensor = (
            torch.tensor([0, 0, -1], device=self.device)
            .unsqueeze(0)
            .expand(self.n_envs, 3)
        )
        self.ones_vec = torch.ones((self.n_envs, 1), device=self.device)
        self.zeros_vec = torch.zeros((self.n_envs, 1), device=self.device)

        # --- Force/Moment Vectors ---
        self.Rs_unit_vector_w = torch.zeros((self.n_envs, 3), device=self.device)
        self.RF_w = torch.zeros((self.n_envs, 3), device=self.device)
        self.RM_w = torch.zeros((self.n_envs, 3), device=self.device)
        self.resultant_force = torch.zeros((self.n_envs, 3), device=self.device)
        self.resultant_moment = torch.zeros((self.n_envs, 3), device=self.device)

        # --- Final Force Scalars (Integrated in 3D) ---
        self.Rs = torch.zeros(self.n_envs, device=self.device)
        self.plate_Rs = torch.zeros(self.n_envs, device=self.device)
        self.edge_Rs = torch.zeros(self.n_envs, device=self.device)
        self.deadload_F = torch.zeros(self.n_envs, device=self.device)

        # --- Intermediate Attributes (Mirroring 2D for compatibility) ---
        # FEE related
        self.rho = torch.zeros((self.n_envs, 1), device=self.device)
        self.ssp_alpha_bar = torch.zeros((self.n_envs, 1), device=self.device)
        self.k = torch.zeros((self.n_envs, 1), device=self.device)
        self.AX = torch.zeros((self.n_envs, 1), device=self.device)
        self.ABX = torch.zeros((self.n_envs, 1), device=self.device)
        self.A2 = torch.zeros((self.n_envs, 1), device=self.device)
        self.BCX = torch.zeros((self.n_envs, 1), device=self.device)
        self.c_star = torch.zeros((self.n_envs, 1), device=self.device)
        self.phi_star = torch.zeros((self.n_envs, 1), device=self.device)
        self.L2 = torch.zeros((self.n_envs, 1), device=self.device)
        self.ha = torch.zeros((self.n_envs, 1), device=self.device)
        self.L3 = torch.zeros((self.n_envs, 1), device=self.device)
        self.hc = torch.zeros((self.n_envs, 1), device=self.device)
        self.z = torch.zeros((self.n_envs, 1), device=self.device)
        self.ADF = torch.zeros((self.n_envs, 1), device=self.device)
        self.W = torch.zeros((self.n_envs, 1), device=self.device)
        self.CF1 = torch.zeros((self.n_envs, 1), device=self.device)
        self.ACF = torch.zeros((self.n_envs, 1), device=self.device)
        self.SF2 = torch.zeros((self.n_envs, 1), device=self.device)
        self.Rs1 = torch.zeros((self.n_envs, 1), device=self.device)
        self.Rs2 = torch.zeros((self.n_envs, 1), device=self.device)
        self.Rs3 = torch.zeros((self.n_envs, 1), device=self.device)
        self.Rs4 = torch.zeros((self.n_envs, 1), device=self.device)
        self.Rs5 = torch.zeros((self.n_envs, 1), device=self.device)
        self.Rs6 = torch.zeros((self.n_envs, 1), device=self.device)
        # Penetration related
        self.p0_2 = torch.zeros((self.n_envs, 1), device=self.device)
        self.p0 = torch.zeros((self.n_envs, 1), device=self.device)
        self.pe = torch.zeros((self.n_envs, 1), device=self.device)

        # Pre-compute constants for efficiency
        self.pi_quarter = np.pi / 4.0
        self.pi_half = np.pi / 2.0
        self.half_tensor = torch.tensor(0.5, device=self.device)
        self.two_tensor = torch.tensor(2.0, device=self.device)

    def _expand_to_samples(self, tensor, num_samples_y):
        """Efficiently expand tensor to [n_envs, num_samples_y, 1] shape.

        Args:
            tensor: Input tensor of various shapes
            num_samples_y: Number of samples in y direction

        Returns:
            Tensor with shape [n_envs, num_samples_y, 1]
        """
        # Ensure tensor is at least 2D with shape [n_envs, 1]
        if tensor.ndim == 0:  # scalar
            tensor = tensor.view(1, 1).expand(self.n_envs, 1)
        elif tensor.ndim == 1:  # [n_envs]
            tensor = tensor.unsqueeze(1)
        # Now tensor is [n_envs, 1], expand to [n_envs, num_samples_y, 1]
        return tensor.unsqueeze(1).expand(self.n_envs, num_samples_y, 1)

    def _batch_expand_parameters(self, param_dict, num_samples_y):
        """Batch expand multiple parameters to sample dimensions.

        Args:
            param_dict: Dictionary of parameter_name -> tensor
            num_samples_y: Number of samples in y direction

        Returns:
            Dictionary of parameter_name -> expanded tensor [n_envs, num_samples_y, 1]
        """
        expanded = {}
        for name, tensor in param_dict.items():
            expanded[name] = self._expand_to_samples(tensor, num_samples_y)
        return expanded

    def update(self):
        """Update all forces in the soil model."""
        with torch.profiler.record_function("SoilForces3D.update"):
            self._update_fee()
            self._update_penetration()
            self._update_deadload()
            self._compute_resultant_COM()

    def _update_fee(self):
        """Update FEE (Fundamental Earthmoving Equation) forces."""
        with torch.profiler.record_function("SoilForces3D._update_fee"):
            # Calculate base soil failure angle (rho) for visualization purposes
            # This is the 2D calculation, used before considering y-slope variations
            self.rho = (self.pi_quarter - (self.SP.phi + self.SP.delta) * self.half_tensor) + (
                self.pi_quarter - self.SSP.beta * self.half_tensor
            )

            # --- Calculate 2D-Analogous Intermediate FEE Variables --- #
            # Ensure calculations use the base self.rho and handle potential NaNs/Infs
            safe_sin_rho = torch.clamp(torch.sin(self.rho), min=1e-5)
            safe_tan_rho = torch.clamp(
                torch.tan(self.rho), min=1e-5
            )  # Avoid div by zero if rho is pi/2

            self.ssp_alpha_bar = self.bucket.alpha_max - self.SSP.alpha
            self.k = np.pi - self.SSP.beta - self.ssp_alpha_bar
            safe_sin_k = torch.clamp(torch.sin(self.k), min=1e-5)

            self.AX = torch.sin(self.ssp_alpha_bar) / safe_sin_k * self.SSP.L
            self.ABX = 0.5 * torch.sin(self.SSP.beta) * self.AX * self.SSP.L
            self.A2 = (
                0.5
                * self.SSP.L
                * self.SSP.L
                * torch.sin(self.SSP.beta)
                * (torch.cos(self.SSP.beta) + torch.sin(self.SSP.beta) / safe_tan_rho)
            )
            self.BCX = self.A2 - self.ABX

            self.A2_safe = torch.clamp(self.A2, min=1e-5)
            self.c_star = torch.where(
                self.A2 > 1e-5,
                (self.SP.ca * self.ABX + self.SP.c * self.BCX) / self.A2_safe,
                self.ones_vec,
            )
            self.phi_star = torch.where(
                self.A2 > 1e-5,
                (self.SP.delta * self.ABX + self.SP.phi * self.BCX) / self.A2_safe,
                self.ones_vec,
            )

            self.L2 = torch.sin(self.rho + self.SSP.beta) / safe_sin_rho * self.SSP.L
            self.ha = torch.sin(self.SP.alpha) * self.L2
            self.L3 = torch.sin(self.SSP.beta) / safe_sin_rho * self.SSP.L
            self.hc = torch.sin(self.SP.alpha + self.rho) * self.L3
            self.z = (self.ha + self.hc) / 3.0

            # Note: Using full bucket width (b) here for 2D analogy
            self.ADF = self.SP.ca * self.bucket.b * self.SSP.L
            self.W = self.SP.gamma * self.bucket.b * self.A2
            self.CF1 = (
                self.SP.c
                * self.bucket.b
                * self.SSP.L
                * torch.sin(self.SSP.beta)
                / safe_sin_rho
            )
            self.ACF = self.c_star * self.A2
            self.SF2 = (
                self.SP.K0 * self.SP.gamma * self.z * torch.tan(self.phi_star) * self.A2
            )

            self.Rs1 = -self.ADF * torch.cos(self.SSP.beta + self.rho + self.SP.phi)
            self.Rs2 = self.W * torch.sin(self.SP.alpha + self.rho + self.SP.phi)
            self.Rs3 = self.CF1 * torch.cos(self.SP.phi)
            self.Rs4 = 2.0 * self.ACF * torch.cos(self.SP.phi)
            self.Rs5 = 2.0 * self.SF2 * torch.cos(self.SP.phi)
            self.Rs6 = torch.sin(self.SSP.beta + self.rho + self.SP.delta + self.SP.phi)
            # --- End of 2D-Analogous Calculations --- #

            # --- 3D Integration using Vectorization --- #
            num_samples_y = getattr(self.cfg, "num_samples_y", 5)

            bucket_pos_x = self.SM.bucket_pos_w[:, 0]  # [n_envs]
            bucket_pos_y_center = self.SM.bucket_pos_w[:, 1]  # [n_envs]

            # Efficiently handle bucket width expansion
            bucket_width_expanded = self._expand_to_samples(self.bucket.b, 1).squeeze(1)  # [n_envs, 1]
            half_width = bucket_width_expanded / 2.0  # [n_envs, 1]

            # Create y_sample_offsets relative to center: [num_samples_y]
            y_sample_offsets = (
                torch.linspace(-1.0, 1.0, num_samples_y, device=self.device)
                .unsqueeze(0)
                .expand(self.n_envs, -1)
                * half_width
            )

            # Absolute y sample positions: [n_envs, num_samples_y]
            sample_y_coords = bucket_pos_y_center.unsqueeze(1) + y_sample_offsets

            # Expand bucket_pos_x for get_angle_to_world: [n_envs, num_samples_y]
            bucket_pos_x_expanded = bucket_pos_x.unsqueeze(1).expand(-1, num_samples_y)

            soil_angles_x, soil_angles_y = self.SM.soil_height.get_angle_to_world(
                bucket_pos_x_expanded, sample_y_coords  # Both [n_envs, num_samples_y]
            )
            # soil_angles_x, soil_angles_y are expected to be [n_envs, num_samples_y]

            # Batch expand all parameters efficiently
            params_to_expand = {
                'phi': self.SP.phi,
                'delta': self.SP.delta,
                'ssp_beta': self.SSP.beta,
                'ssp_alpha': self.SSP.alpha,
                'bucket_alpha_max': self.bucket.alpha_max,
                'ssp_L': self.SSP.L,
                'sp_ca': self.SP.ca,
                'sp_c': self.SP.c,
                'sp_gamma': self.SP.gamma,
                'sp_K0': self.SP.K0,
                'sp_alpha': self.SP.alpha
            }

            expanded_params = self._batch_expand_parameters(params_to_expand, num_samples_y)

            # Extract expanded parameters with shorter names
            phi_exp = expanded_params['phi']
            delta_exp = expanded_params['delta']
            ssp_beta_exp = expanded_params['ssp_beta']
            ssp_alpha_exp = expanded_params['ssp_alpha']
            bucket_alpha_max_exp = expanded_params['bucket_alpha_max']
            ssp_L_exp = expanded_params['ssp_L']
            sp_ca_exp = expanded_params['sp_ca']
            sp_c_exp = expanded_params['sp_c']
            sp_gamma_exp = expanded_params['sp_gamma']
            sp_K0_exp = expanded_params['sp_K0']
            sp_alpha_exp = expanded_params['sp_alpha']

            # soil_angle_x/y from get_angle_to_world are [n_envs, num_samples_y], need to be [n_envs, num_samples_y, 1]
            soil_angle_x_exp = soil_angles_x.unsqueeze(2)
            soil_angle_y_exp = soil_angles_y.unsqueeze(2)

            # Calculate 3D rho considering soil angles at each sample point
            local_rho = (self.pi_quarter - (phi_exp + delta_exp) * self.half_tensor) + (
                self.pi_quarter - ssp_beta_exp * self.half_tensor
            )
            x_angle_factor = torch.abs(torch.sin(soil_angle_x_exp))
            y_angle_factor = torch.abs(torch.sin(soil_angle_y_exp))
            combined_angle_factor = 0.7 * x_angle_factor + 0.3 * y_angle_factor
            local_rho = local_rho * (
                1.0 - 0.2 * combined_angle_factor
            )  # Shape: [n_envs, num_samples_y, 1]

            # Compute local versions of key parameters (all ops are now on [n_envs, num_samples_y, 1] tensors)
            local_ssp_alpha_bar = bucket_alpha_max_exp - ssp_alpha_exp
            local_k = np.pi - ssp_beta_exp - local_ssp_alpha_bar
            safe_sin_k_local = torch.clamp(torch.sin(local_k), min=1e-5)
            safe_tan_rho_local = torch.clamp(torch.tan(local_rho), min=1e-5)
            safe_sin_rho_local = torch.clamp(torch.sin(local_rho), min=1e-5)

            local_AX = torch.sin(local_ssp_alpha_bar) / safe_sin_k_local * ssp_L_exp
            local_ABX = self.half_tensor * torch.sin(ssp_beta_exp) * local_AX * ssp_L_exp
            local_A2 = (
                self.half_tensor
                * ssp_L_exp
                * ssp_L_exp
                * torch.sin(ssp_beta_exp)
                * (
                    torch.cos(ssp_beta_exp)
                    + torch.sin(ssp_beta_exp) / safe_tan_rho_local
                )
            )
            local_BCX = local_A2 - local_ABX

            local_A2_safe = torch.clamp(local_A2, min=1e-5)
            # Create ones tensor with proper shape - reuse for efficiency
            ones_vec_exp = torch.ones(
                (self.n_envs, num_samples_y, 1), device=self.device
            )

            local_c_star = torch.where(
                local_A2 > 1e-5,
                (sp_ca_exp * local_ABX + sp_c_exp * local_BCX) / local_A2_safe,
                ones_vec_exp,
            )
            local_phi_star = torch.where(
                local_A2 > 1e-5,
                (delta_exp * local_ABX + phi_exp * local_BCX) / local_A2_safe,
                ones_vec_exp,
            )

            local_L2 = (
                torch.sin(local_rho + ssp_beta_exp) / safe_sin_rho_local * ssp_L_exp
            )
            local_ha = torch.sin(sp_alpha_exp) * local_L2
            local_L3 = torch.sin(ssp_beta_exp) / safe_sin_rho_local * ssp_L_exp
            local_hc = torch.sin(sp_alpha_exp + local_rho) * local_L3
            local_z = (local_ha + local_hc) / 3.0

            segment_width = (
                bucket_width_expanded / num_samples_y
            )  # [n_envs,1], needs to be [n_envs, num_samples_y, 1]
            segment_width_exp = segment_width.unsqueeze(1).expand(
                self.n_envs, num_samples_y, 1
            )

            local_ADF = sp_ca_exp * segment_width_exp * ssp_L_exp
            local_W = sp_gamma_exp * segment_width_exp * local_A2
            local_CF1 = (
                sp_c_exp
                * segment_width_exp
                * ssp_L_exp
                * torch.sin(ssp_beta_exp)
                / safe_sin_rho_local
            )

            # ACF and SF2 were scaled by (segment_width / bucket_width) in loop.
            # bucket_width_expanded is [n_envs,1]. segment_width_exp is [n_envs, num_samples_y, 1]
            # The ratio is 1/num_samples_y
            scaling_factor = 1.0 / num_samples_y

            local_ACF = local_c_star * local_A2 * scaling_factor
            local_SF2 = (
                sp_K0_exp
                * sp_gamma_exp
                * local_z
                * torch.tan(local_phi_star)
                * local_A2
                * scaling_factor
            )

            local_Rs1 = -local_ADF * torch.cos(ssp_beta_exp + local_rho + phi_exp)
            local_Rs2 = local_W * torch.sin(sp_alpha_exp + local_rho + phi_exp)
            local_Rs3 = local_CF1 * torch.cos(phi_exp)
            local_Rs4 = 2.0 * local_ACF * torch.cos(phi_exp)
            local_Rs5 = 2.0 * local_SF2 * torch.cos(phi_exp)
            local_Rs6_denom = torch.sin(ssp_beta_exp + local_rho + delta_exp + phi_exp)

            numerator = local_Rs1 + local_Rs2 + local_Rs3 + local_Rs4 + local_Rs5
            denominator = torch.clamp(local_Rs6_denom, min=1e-5)

            max_force_magnitude = 1.0e6
            numerator = torch.clamp(
                numerator, -max_force_magnitude, max_force_magnitude
            )
            numerator = torch.where(
                torch.isfinite(numerator), numerator, torch.zeros_like(numerator)
            )

            local_Rs_per_sample = (
                numerator / denominator
            )  # Shape [n_envs, num_samples_y, 1]

            # Zero out values from invalid calculations
            # depth_mask was [n_envs]. Need to expand for samples: [n_envs, num_samples_y, 1]
            depth_mask_expanded = (
                self.bucket.depth.unsqueeze(1).expand(-1, num_samples_y, -1) <= 0.0
            )
            invalid_denom_mask = torch.abs(denominator) < 1e-5
            force_too_large_mask = torch.abs(local_Rs_per_sample) > max_force_magnitude

            invalid_mask_combined = (
                depth_mask_expanded | invalid_denom_mask | force_too_large_mask
            )
            local_Rs_per_sample = local_Rs_per_sample * (~invalid_mask_combined).float()

            # Sum across the samples dimension
            total_rs = torch.sum(local_Rs_per_sample, dim=1).squeeze(
                1
            )  # Shape [n_envs]

            self.Rs = total_rs * self.cfg.fee_multiplyer

            # Direction of force in world frame (using base SP.alpha, SSP.beta, SP.delta)
            # These are the original [n_envs,1] tensors, not the expanded ones
            fee_force_angle_w = -(
                self.pi_half + self.SP.alpha - self.SSP.beta - self.SP.delta
            )
            fee_cos = torch.cos(fee_force_angle_w)
            fee_sin = torch.sin(fee_force_angle_w)

            if len(fee_cos.shape) > 1:
                fee_cos = fee_cos.view(self.n_envs)
            if len(fee_sin.shape) > 1:
                fee_sin = fee_sin.view(self.n_envs)

            self.Rs_unit_vector_w[:, 0] = fee_cos
            self.Rs_unit_vector_w[:, 2] = fee_sin

            # Adjust y-component and re-normalize (original logic for Rs_unit_vector_w)
            # This part used average angles at bucket_pos_y_center. It can remain as is.
            avg_soil_angles_x, avg_soil_angles_y = (
                self.SM.soil_height.get_angle_to_world(
                    bucket_pos_x.unsqueeze(1),
                    bucket_pos_y_center.unsqueeze(1),  # Query at center y
                )
            )
            # avg_soil_angles_x/y are [n_envs, 1]
            avg_angle_x = avg_soil_angles_x.view(self.n_envs)
            avg_angle_y = avg_soil_angles_y.view(self.n_envs)

            x_sin = torch.sin(avg_angle_x)
            y_sin = torch.sin(avg_angle_y)

            self.Rs_unit_vector_w[:, 1] = -y_sin * 0.5
            x_angle_factor = torch.abs(x_sin) * 0.1
            self.Rs_unit_vector_w[:, 0] = self.Rs_unit_vector_w[:, 0] * (
                1.0 - x_angle_factor
            )
            self.Rs_unit_vector_w[:, 2] = self.Rs_unit_vector_w[:, 2] * (
                1.0 - x_angle_factor
            )

            norm = torch.norm(self.Rs_unit_vector_w, dim=1, keepdim=True)
            self.Rs_unit_vector_w = self.Rs_unit_vector_w / torch.clamp(norm, min=1e-5)

    def _update_penetration(self):
        """Update penetration forces (plate and edge forces)."""
        with torch.profiler.record_function("SoilForces3D._update_penetration"):
            # --- Calculate 2D-Analogous Intermediate Penetration Variables --- #
            # Plate
            self.p0_2 = (
                self.half_tensor
                * self.SP.gamma
                * self.bucket.clipped_average_depth_bp
                * (
                    (1.0 + self.SP.K0)
                    + (1.0 - self.SP.K0)
                    * torch.cos(self.two_tensor * self.bucket.bp_angle_to_horizon)
                )
            )
            # Tip
            self.p0 = (
                self.half_tensor
                * self.SP.gamma
                * self.bucket.clipped_depth
                * (
                    (1.0 + self.SP.K0)
                    + (1.0 - self.SP.K0)
                    * torch.cos(self.two_tensor * self.bucket.bp_angle_to_horizon)
                )
            )
            self.pe = self.p0 * self.SP.CP
            # --- End of 2D-Analogous Calculations --- #

            # --- 3D Integration Loop using Vectorization --- #
            num_samples_y = getattr(
                self.cfg, "num_penetration_samples_y", 5
            )  # Configurable samples

            bucket_pos_x = self.SM.bucket_pos_w[:, 0]  # [n_envs]
            bucket_pos_y_center = self.SM.bucket_pos_w[:, 1]  # [n_envs]
            bucket_pos_z_tip = self.SM.bucket_pos_w[:, 2]  # [n_envs], for depth calculation

            # Efficiently handle bucket width expansion
            bucket_width_expanded = self._expand_to_samples(self.bucket.b, 1).squeeze(1)  # [n_envs, 1]
            half_width = bucket_width_expanded / 2.0  # [n_envs, 1]

            y_sample_offsets = (
                torch.linspace(-1.0, 1.0, num_samples_y, device=self.device)
                .unsqueeze(0)
                .expand(self.n_envs, -1)
                * half_width
            )
            sample_y_coords = (
                bucket_pos_y_center.unsqueeze(1) + y_sample_offsets
            )  # [n_envs, num_samples_y]
            bucket_pos_x_expanded = bucket_pos_x.unsqueeze(1).expand(
                -1, num_samples_y
            )  # [n_envs, num_samples_y]

            # Get soil heights at all sample points: [n_envs, num_samples_y]
            soil_heights_at_samples = self.SM.soil_height.get_height(
                bucket_pos_x_expanded, sample_y_coords
            )
            if soil_heights_at_samples.dim() > 2:  # Should be [n_envs, num_samples_y]
                soil_heights_at_samples = soil_heights_at_samples.squeeze(
                    -1
                )  # if it came as [n_envs, num_samples_y, 1]

            # Calculate depth at each sample point: [n_envs, num_samples_y]
            local_bucket_depth = torch.clamp(
                bucket_pos_z_tip.unsqueeze(1) - soil_heights_at_samples, min=0.0
            )

            # Batch expand all penetration parameters efficiently
            penetration_params = {
                'gamma': self.SP.gamma,
                'K0': self.SP.K0,
                'bp_angle_horizon': self.bucket.bp_angle_to_horizon,
                'ca': self.SP.ca,
                'delta': self.SP.delta,
                'bp_soil': self.bucket.bp_soil,
                'CP': self.SP.CP,
                'half_angle': self.bucket.half_angle,
                'top_width': self.bucket.top_width
            }

            expanded_pen_params = self._batch_expand_parameters(penetration_params, num_samples_y)

            # Extract with shorter names
            gamma_exp = expanded_pen_params['gamma']
            K0_exp = expanded_pen_params['K0']
            bp_angle_horizon_exp = expanded_pen_params['bp_angle_horizon']
            ca_exp = expanded_pen_params['ca']
            delta_exp = expanded_pen_params['delta']
            bp_soil_exp = expanded_pen_params['bp_soil']
            CP_exp = expanded_pen_params['CP']
            half_angle_exp = expanded_pen_params['half_angle']
            top_width_exp = expanded_pen_params['top_width']

            # Handle vel_cos expansion to [n_envs, num_samples_y] (no last dimension)
            vel_cos_temp = self.bucket.vel_cos
            if vel_cos_temp.ndim == 0:  # scalar
                vel_cos_exp = vel_cos_temp.view(1, 1).expand(self.n_envs, num_samples_y)
            elif vel_cos_temp.ndim == 1:  # [n_envs]
                vel_cos_exp = vel_cos_temp.unsqueeze(1).expand(self.n_envs, num_samples_y)
            else:  # [n_envs, 1] or [n_envs, num_samples_y]
                if vel_cos_temp.shape[1] == 1:
                    vel_cos_exp = vel_cos_temp.expand(self.n_envs, num_samples_y)
                else:  # [n_envs, num_samples_y]
                    vel_cos_exp = vel_cos_temp

            # Plate penetration calculations
            # local_bucket_depth needs to be [..., 1] for broadcasting with gamma_exp etc.
            local_p0_2_samples = (
                self.half_tensor
                * gamma_exp
                * local_bucket_depth.unsqueeze(2)
                * (
                    (1.0 + K0_exp)
                    + (1.0 - K0_exp) * torch.cos(self.two_tensor * bp_angle_horizon_exp)
                )
            )

            segment_width = bucket_width_expanded / num_samples_y  # [n_envs, 1]
            segment_width_exp = segment_width.unsqueeze(1).expand(
                -1, num_samples_y, -1
            )  # [n_envs, num_samples_y, 1]

            local_plate_rs_samples = (
                (ca_exp + local_p0_2_samples * torch.tan(delta_exp))
                * bp_soil_exp
                * segment_width_exp
            )

            # Edge penetration calculations
            local_p0_samples = (
                self.half_tensor
                * gamma_exp
                * local_bucket_depth.unsqueeze(2)
                * (
                    (1.0 + K0_exp)
                    + (1.0 - K0_exp) * torch.cos(self.two_tensor * bp_angle_horizon_exp)
                )
            )
            local_pe_samples = local_p0_samples * CP_exp

            safe_tan_half_angle_exp = torch.clamp(torch.tan(half_angle_exp), min=1e-5)
            delta_term_samples = ca_exp + local_pe_samples * torch.clamp(
                torch.tan(delta_exp), min=-10.0, max=10.0
            )
            edge_term_samples = (
                local_pe_samples + delta_term_samples / safe_tan_half_angle_exp
            )
            edge_term_samples = torch.clamp(edge_term_samples, min=0.0, max=1.0e6)

            local_edge_rs_samples = (
                edge_term_samples * segment_width_exp * top_width_exp
            )

            # Apply velocity factor and in-soil mask to edge forces
            vel_factor = torch.sigmoid(vel_cos_exp * 5.0) * torch.clamp(
                vel_cos_exp, min=0.0
            )  # [n_envs, num_samples_y]
            local_edge_rs_samples = local_edge_rs_samples * vel_factor.unsqueeze(
                2
            )  # Multiply by [n_envs, num_samples_y, 1]

            in_soil_mask_samples = (
                (local_bucket_depth > 0.0).unsqueeze(2).float()
            )  # [n_envs, num_samples_y, 1]
            local_edge_rs_samples = local_edge_rs_samples * in_soil_mask_samples
            local_plate_rs_samples = local_plate_rs_samples * in_soil_mask_samples

            # Handle NaN/Inf
            local_edge_rs_samples = torch.where(
                torch.isfinite(local_edge_rs_samples),
                local_edge_rs_samples,
                torch.zeros_like(local_edge_rs_samples),
            )
            local_plate_rs_samples = torch.where(
                torch.isfinite(local_plate_rs_samples),
                local_plate_rs_samples,
                torch.zeros_like(local_plate_rs_samples),
            )

            # Sum across samples dimension
            total_plate_rs = torch.sum(local_plate_rs_samples, dim=1).squeeze(
                1
            )  # [n_envs]
            total_edge_rs = torch.sum(local_edge_rs_samples, dim=1).squeeze(
                1
            )  # [n_envs]

            self.plate_Rs = total_plate_rs * self.cfg.penetration_plate_multiplyer
            self.edge_Rs = total_edge_rs * self.cfg.penetration_edge_multiplyer

    def _update_deadload(self):
        """Update the deadload force, which is the weight of the soil in the bucket.

        For the 3D model, we compute the full volumetric soil load in the bucket.
        """
        with torch.profiler.record_function("SoilForces3D._update_deadload"):
            # Initialize soil volume
            soil_volume = torch.zeros(self.n_envs, device=self.device)

            # Use volume if available directly from SSP
            # Note: Currently SSP3D doesn't have a volume attribute, but this is prepared for future extensions
            if hasattr(self.SSP, "B") and hasattr(self.bucket, "b"):
                # Simple integration using cross-sectional area and bucket width
                cross_section_area = self.SSP.B
                # Ensure it has shape [n_envs]
                if len(cross_section_area.shape) > 1:
                    cross_section_area = cross_section_area.sum(
                        dim=tuple(range(1, len(cross_section_area.shape)))
                    )

                # Calculate volume = area × width
                bucket_width = self.bucket.b
                soil_volume = cross_section_area * bucket_width
            else:
                # Simple integration using a discrete number of slices
                bucket_width = self.bucket.b

                # Get cross-sectional area from SSP (simplified approach)
                cross_section_area = self.SSP.B
                # Ensure it has shape [n_envs]
                if len(cross_section_area.shape) > 1:
                    cross_section_area = cross_section_area.sum(
                        dim=tuple(range(1, len(cross_section_area.shape)))
                    )

                # Calculate volume = area × width
                soil_volume = cross_section_area * bucket_width

            # Apply gamma (soil density) to get force
            # Handle gamma properly whether it's a scalar or tensor

            gamma_tensor = self.SP.gamma
            if len(gamma_tensor.shape) > 1:
                gamma = gamma_tensor.sum(dim=tuple(range(1, len(gamma_tensor.shape))))
            else:
                gamma = gamma_tensor
                # Ensure it broadcasts correctly if it's a single value tensor
                if gamma.numel() == 1:
                    gamma = gamma.expand(self.n_envs)

            # Calculate deadload force
            self.deadload_F = gamma * soil_volume
            self.deadload_F *= self.cfg.deadload_multiplyer

    def _compute_resultant_COM(self):
        """
        Compute resultant force and moment at the COM of the bucket.

        For the 3D model, properly accounts for forces and moments in all three dimensions.
        """
        # Ensure scalar forces have shape [n_envs, 1] for broadcasting with 3D vectors
        Rs_force = self.Rs.view(self.n_envs, 1)
        Rs_vec = Rs_force * self.Rs_unit_vector_w

        # Penetration forces
        pen_sum = self.edge_Rs + self.plate_Rs
        pen_force = pen_sum.view(self.n_envs, 1)
        penF_vec = pen_force * self.SM.bp_unit_vector_w

        # Deadload force
        deadload_force = self.deadload_F.view(self.n_envs, 1)
        deadloadF_vec = deadload_force * self.zero_tensor

        # Compute total resultant force
        self.RF_w = Rs_vec + penF_vec + deadloadF_vec

        # Create bucket COM position in 3D
        bucket_com_pos_3d = torch.zeros((self.n_envs, 3), device=self.device)

        # Handle bucket COM position - simplify to just use 3D position directly if available
        bucket_com_pos = self.bucket.COM_pos_w

        # If bucket COM is in 2D format (legacy 2D model), convert to 3D
        bucket_com_pos_3d = bucket_com_pos.view(self.n_envs, 3)

        # Position vectors for application points of forces
        Rs_pos_3d = torch.zeros((self.n_envs, 3), device=self.device)

        # Extract SSP.L safely to ensure it has shape [n_envs]
        ssp_L = self.SSP.L
        if len(ssp_L.shape) > 1:
            ssp_L = ssp_L.view(self.n_envs)

        # Calculate positions for fee forces using SSP unit vector
        # SSP unit vector already has shape [n_envs, 3]
        Rs_pos_3d[:, 0] = (
            self.SM.bucket_pos_w[:, 0] + self.SSP.ssp_unit_vector_w[:, 0] * ssp_L / 2.0
        )
        Rs_pos_3d[:, 1] = self.SM.bucket_pos_w[:, 1]  # y position
        Rs_pos_3d[:, 2] = (
            self.SM.bucket_pos_w[:, 2] + self.SSP.ssp_unit_vector_w[:, 2] * ssp_L / 2.0
        )

        # For penetration forces, use the bucket tip position in 3D
        penF_pos_3d = self.SM.bucket_pos_w.clone()

        # For deadload, use centroid position if available
        deadloadF_pos_3d = torch.zeros((self.n_envs, 3), device=self.device)

        centroid_pos = self.SSP.centroid_pos_w
        deadloadF_pos_3d = centroid_pos.view(self.n_envs, 3)

        # Calculate position vectors relative to COM for moment calculation
        Rs_rel_pos = Rs_pos_3d - bucket_com_pos_3d
        penF_rel_pos = penF_pos_3d - bucket_com_pos_3d
        deadloadF_rel_pos = deadloadF_pos_3d - bucket_com_pos_3d

        # Calculate moments using cross products
        M_Rs = torch.cross(Rs_rel_pos, Rs_vec, dim=1)
        M_pen = torch.cross(penF_rel_pos, penF_vec, dim=1)
        M_deadload = torch.cross(deadloadF_rel_pos, deadloadF_vec, dim=1)

        # Sum all moment components for total moment
        self.RM_w = M_Rs + M_pen + M_deadload

        # Store final resultant forces and moments
        self.resultant_force = self.RF_w
        self.resultant_moment = self.RM_w
